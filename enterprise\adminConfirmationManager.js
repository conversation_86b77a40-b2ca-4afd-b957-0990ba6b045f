/**
 * Admin Confirmation Manager
 * Handles admin confirmation for role matching with extra text elements
 */

const EventEmitter = require('events');
const roleNameNormalizer = require('../utils/roleNameNormalizer');

class AdminConfirmationManager extends EventEmitter {
    constructor() {
        super();

        // Load configuration
        this.config = this.loadConfiguration();

        // Guild-specific configurations
        this.guildConfigs = new Map();
        
        // Pending confirmations storage
        this.pendingConfirmations = new Map();
        
        // Extra text patterns to detect
        this.extraTextPatterns = {
            // Common suffixes that indicate additional context
            suffixes: [
                /\s+(idb|temp|backup|old|new|test|dev|prod|beta|alpha)$/i,
                /\s+\[(verified|premium|vip|special|admin|mod)\]$/i,
                /\s+\((active|inactive|pending|trial)\)$/i,
                /-+(temp|backup|old|new|test|dev|prod|beta|alpha)$/i,
                /_+(temp|backup|old|new|test|dev|prod|beta|alpha)$/i
            ],
            
            // Common prefixes that indicate additional context
            prefixes: [
                /^(temp|backup|old|new|test|dev|prod|beta|alpha)\s+/i,
                /^(ex|former|previous)\s+/i,
                /^\[(verified|premium|vip|special)\]\s+/i,
                /^\((active|inactive|pending|trial)\)\s+/i
            ],
            
            // Middle insertions that indicate additional context
            insertions: [
                /\s+(v\d+|\d+\.\d+)\s+/i, // Version numbers
                /\s+\[(.*?)\]\s+/i,        // Bracketed text
                /\s+\((.*?)\)\s+/i,        // Parenthetical text
                /\s+-\s+(.*?)\s+-\s+/i     // Dashed insertions
            ]
        };
        
        // Statistics tracking
        this.stats = {
            totalConfirmationsRequested: 0,
            totalConfirmationsApproved: 0,
            totalConfirmationsRejected: 0,
            totalConfirmationsTimedOut: 0,
            averageResponseTime: 0
        };
        
        console.log('[ADMIN_CONFIRMATION] Admin confirmation manager initialized');
    }

    /**
     * Load configuration from file
     * @returns {Object} Configuration object
     */
    loadConfiguration() {
        try {
            const fs = require('fs');
            const path = require('path');
            const configPath = path.join(__dirname, '..', 'config', 'adminConfirmation.json');

            if (fs.existsSync(configPath)) {
                const configData = fs.readFileSync(configPath, 'utf8');
                const config = JSON.parse(configData);
                return config.adminConfirmation;
            }
        } catch (error) {
            console.warn('[ADMIN_CONFIRMATION] Failed to load configuration, using defaults:', error.message);
        }

        // Default configuration
        return {
            enabled: true,
            similarityThresholdForConfirmation: 80,
            maxPendingConfirmations: 50,
            confirmationTimeout: 300000,
            highlightDifferences: true,
            autoApproveIfNoAdmin: false
        };
    }

    /**
     * Get configuration for specific guild
     * @param {string} guildId - Guild ID
     * @returns {Object} Guild-specific configuration
     */
    getGuildConfig(guildId) {
        if (this.guildConfigs.has(guildId)) {
            return this.guildConfigs.get(guildId);
        }

        // Try to load guild-specific config
        try {
            const fs = require('fs');
            const path = require('path');
            const configPath = path.join(__dirname, '..', 'config', 'adminConfirmation.json');

            if (fs.existsSync(configPath)) {
                const configData = fs.readFileSync(configPath, 'utf8');
                const fullConfig = JSON.parse(configData);

                if (fullConfig.guildSpecificSettings && fullConfig.guildSpecificSettings[guildId]) {
                    const guildConfig = { ...this.config, ...fullConfig.guildSpecificSettings[guildId] };
                    this.guildConfigs.set(guildId, guildConfig);
                    return guildConfig;
                }
            }
        } catch (error) {
            console.warn(`[ADMIN_CONFIRMATION] Failed to load guild config for ${guildId}:`, error.message);
        }

        // Use default config
        this.guildConfigs.set(guildId, this.config);
        return this.config;
    }

    /**
     * Analyze if a role match requires admin confirmation
     * @param {string} intendedRoleName - The intended role name
     * @param {Object} existingRole - The existing role object
     * @param {number} similarityScore - The similarity score
     * @param {string} guildId - Guild ID for configuration
     * @returns {Object} Analysis result
     */
    analyzeRoleMatch(intendedRoleName, existingRole, similarityScore, guildId = null) {
        const config = guildId ? this.getGuildConfig(guildId) : this.config;

        if (!config.enabled) {
            return { requiresConfirmation: false, reason: 'Admin confirmation disabled' };
        }

        if (similarityScore < config.similarityThresholdForConfirmation) {
            return { requiresConfirmation: false, reason: 'Below similarity threshold' };
        }
        
        // Normalize both names for comparison
        const normalizedIntended = roleNameNormalizer.normalize(intendedRoleName);
        const normalizedExisting = roleNameNormalizer.normalize(existingRole.name);
        
        // Check if they're exactly the same after normalization
        if (normalizedIntended === normalizedExisting) {
            return { requiresConfirmation: false, reason: 'Exact match after normalization' };
        }
        
        // Detect extra text elements
        const extraTextAnalysis = this.detectExtraTextElements(intendedRoleName, existingRole.name);
        
        if (extraTextAnalysis.hasExtraText) {
            return {
                requiresConfirmation: true,
                reason: 'Extra text elements detected',
                analysis: extraTextAnalysis,
                intendedName: intendedRoleName,
                existingName: existingRole.name,
                normalizedIntended: normalizedIntended,
                normalizedExisting: normalizedExisting,
                similarityScore: similarityScore
            };
        }
        
        return { requiresConfirmation: false, reason: 'No extra text elements detected' };
    }

    /**
     * Detect extra text elements in existing role name
     * @param {string} intendedName - Intended role name
     * @param {string} existingName - Existing role name
     * @returns {Object} Detection result
     */
    detectExtraTextElements(intendedName, existingName) {
        const normalizedIntended = roleNameNormalizer.normalize(intendedName);
        const normalizedExisting = roleNameNormalizer.normalize(existingName);
        
        // If they're the same after normalization, no extra text
        if (normalizedIntended === normalizedExisting) {
            return { hasExtraText: false };
        }
        
        const detectedPatterns = [];
        const extraElements = [];
        
        // Check for suffix patterns
        this.extraTextPatterns.suffixes.forEach(pattern => {
            const match = existingName.match(pattern);
            if (match) {
                const withoutSuffix = existingName.replace(pattern, '').trim();
                const normalizedWithoutSuffix = roleNameNormalizer.normalize(withoutSuffix);
                
                if (normalizedWithoutSuffix === normalizedIntended) {
                    detectedPatterns.push({ type: 'suffix', pattern: match[0], position: 'end' });
                    extraElements.push(match[0]);
                }
            }
        });
        
        // Check for prefix patterns
        this.extraTextPatterns.prefixes.forEach(pattern => {
            const match = existingName.match(pattern);
            if (match) {
                const withoutPrefix = existingName.replace(pattern, '').trim();
                const normalizedWithoutPrefix = roleNameNormalizer.normalize(withoutPrefix);
                
                if (normalizedWithoutPrefix === normalizedIntended) {
                    detectedPatterns.push({ type: 'prefix', pattern: match[0], position: 'start' });
                    extraElements.push(match[0]);
                }
            }
        });
        
        // Check for insertion patterns
        this.extraTextPatterns.insertions.forEach(pattern => {
            const match = existingName.match(pattern);
            if (match) {
                const withoutInsertion = existingName.replace(pattern, ' ').replace(/\s+/g, ' ').trim();
                const normalizedWithoutInsertion = roleNameNormalizer.normalize(withoutInsertion);
                
                if (normalizedWithoutInsertion === normalizedIntended) {
                    detectedPatterns.push({ type: 'insertion', pattern: match[0], position: 'middle' });
                    extraElements.push(match[0]);
                }
            }
        });
        
        // Check for general extra words/elements
        const intendedWords = normalizedIntended.split(' ').filter(w => w.length > 0);
        const existingWords = normalizedExisting.split(' ').filter(w => w.length > 0);
        
        const extraWords = existingWords.filter(word => !intendedWords.includes(word));
        const missingWords = intendedWords.filter(word => !existingWords.includes(word));
        
        // If there are extra words but no missing words, it's likely extra text
        if (extraWords.length > 0 && missingWords.length === 0) {
            extraWords.forEach(word => {
                if (!extraElements.includes(word)) {
                    detectedPatterns.push({ type: 'extra_word', pattern: word, position: 'unknown' });
                    extraElements.push(word);
                }
            });
        }
        
        return {
            hasExtraText: detectedPatterns.length > 0,
            detectedPatterns: detectedPatterns,
            extraElements: extraElements,
            intendedWords: intendedWords,
            existingWords: existingWords,
            extraWords: extraWords,
            missingWords: missingWords
        };
    }

    /**
     * Create admin confirmation interface
     * @param {Object} confirmationData - Data for confirmation
     * @returns {Object} Discord embed and components
     */
    createConfirmationInterface(confirmationData) {
        const { intendedName, existingName, analysis, similarityScore, sessionId } = confirmationData;
        
        // Create highlighted comparison
        const highlightedComparison = this.createHighlightedComparison(intendedName, existingName, analysis);
        
        // Create embed
        const embed = {
            color: 0xFFA500, // Orange for warning/confirmation
            title: '⚠️ Admin Confirmation Required',
            description: 'The automatic role matching system found a potential match that contains extra text elements. Please review and confirm the action.',
            fields: [
                {
                    name: '🎯 Intended Channel/Role Name',
                    value: `\`${intendedName}\``,
                    inline: true
                },
                {
                    name: '🔍 Found Existing Role',
                    value: `\`${existingName}\``,
                    inline: true
                },
                {
                    name: '📊 Similarity Score',
                    value: `${similarityScore}%`,
                    inline: true
                },
                {
                    name: '🔎 Detected Extra Elements',
                    value: analysis.extraElements.length > 0 ? 
                        analysis.extraElements.map(elem => `\`${elem}\``).join(', ') : 
                        'General text differences',
                    inline: false
                },
                {
                    name: '📝 Comparison',
                    value: highlightedComparison,
                    inline: false
                },
                {
                    name: '❓ What would you like to do?',
                    value: '• **Use Existing Role**: Use the found role despite the extra text\n• **Create New Role**: Create a new role with the exact intended name',
                    inline: false
                }
            ],
            footer: {
                text: `Session ID: ${sessionId} | This confirmation will expire in 5 minutes`
            },
            timestamp: new Date().toISOString()
        };
        
        // Create action buttons
        const components = [
            {
                type: 1, // Action Row
                components: [
                    {
                        type: 2, // Button
                        style: 3, // Success (Green)
                        label: 'Use Existing Role',
                        custom_id: `admin_confirm_use_${sessionId}`,
                        emoji: { name: '✅' }
                    },
                    {
                        type: 2, // Button
                        style: 4, // Danger (Red)
                        label: 'Create New Role',
                        custom_id: `admin_confirm_create_${sessionId}`,
                        emoji: { name: '➕' }
                    },
                    {
                        type: 2, // Button
                        style: 2, // Secondary (Gray)
                        label: 'View Details',
                        custom_id: `admin_confirm_details_${sessionId}`,
                        emoji: { name: '📋' }
                    }
                ]
            }
        ];
        
        return { embed, components };
    }

    /**
     * Create highlighted comparison showing differences
     * @param {string} intendedName - Intended name
     * @param {string} existingName - Existing name
     * @param {Object} analysis - Extra text analysis
     * @returns {string} Highlighted comparison
     */
    createHighlightedComparison(intendedName, existingName, analysis) {
        if (!this.config.highlightDifferences) {
            return `Intended: \`${intendedName}\`\nExisting: \`${existingName}\``;
        }
        
        let highlightedExisting = existingName;
        
        // Highlight detected extra elements
        analysis.extraElements.forEach(element => {
            const escapedElement = element.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            const regex = new RegExp(`(${escapedElement})`, 'gi');
            highlightedExisting = highlightedExisting.replace(regex, '**[$1]**');
        });
        
        return `Intended: \`${intendedName}\`\nExisting: ${highlightedExisting}\n\n*Extra elements are highlighted in **[brackets]***`;
    }

    /**
     * Store pending confirmation
     * @param {string} sessionId - Session ID
     * @param {Object} confirmationData - Confirmation data
     */
    storePendingConfirmation(sessionId, confirmationData) {
        const confirmation = {
            ...confirmationData,
            timestamp: Date.now(),
            timeout: setTimeout(() => {
                this.handleConfirmationTimeout(sessionId);
            }, this.config.confirmationTimeout)
        };
        
        this.pendingConfirmations.set(sessionId, confirmation);
        this.stats.totalConfirmationsRequested++;
        
        // Clean up old confirmations if we have too many
        if (this.pendingConfirmations.size > this.config.maxPendingConfirmations) {
            this.cleanupOldConfirmations();
        }
        
        console.log(`[ADMIN_CONFIRMATION] Stored pending confirmation: ${sessionId}`);
    }

    /**
     * Handle admin confirmation response
     * @param {string} sessionId - Session ID
     * @param {string} action - Action taken (use, create, details)
     * @param {Object} interaction - Discord interaction
     * @returns {Object} Response result
     */
    async handleConfirmationResponse(sessionId, action, interaction) {
        const confirmation = this.pendingConfirmations.get(sessionId);
        
        if (!confirmation) {
            return {
                success: false,
                error: 'Confirmation session not found or expired',
                ephemeral: true
            };
        }
        
        // Clear timeout
        clearTimeout(confirmation.timeout);
        
        const responseTime = Date.now() - confirmation.timestamp;
        this.updateAverageResponseTime(responseTime);
        
        try {
            let result;
            
            switch (action) {
                case 'use':
                    result = await this.handleUseExistingRole(confirmation, interaction);
                    this.stats.totalConfirmationsApproved++;
                    break;
                    
                case 'create':
                    result = await this.handleCreateNewRole(confirmation, interaction);
                    this.stats.totalConfirmationsRejected++;
                    break;
                    
                case 'details':
                    result = await this.handleShowDetails(confirmation, interaction);
                    // Don't remove confirmation for details view
                    return result;
                    
                default:
                    result = {
                        success: false,
                        error: 'Unknown action',
                        ephemeral: true
                    };
            }
            
            // Remove confirmation after processing
            this.pendingConfirmations.delete(sessionId);
            
            // Emit event for tracking
            this.emit('confirmationProcessed', {
                sessionId,
                action,
                responseTime,
                success: result.success
            });
            
            return result;
            
        } catch (error) {
            console.error(`[ADMIN_CONFIRMATION] Error handling confirmation ${sessionId}:`, error);
            this.pendingConfirmations.delete(sessionId);
            
            return {
                success: false,
                error: `Failed to process confirmation: ${error.message}`,
                ephemeral: true
            };
        }
    }

    /**
     * Handle using existing role
     * @param {Object} confirmation - Confirmation data
     * @param {Object} interaction - Discord interaction
     * @returns {Object} Result
     */
    async handleUseExistingRole(confirmation, interaction) {
        console.log(`[ADMIN_CONFIRMATION] Admin approved using existing role: ${confirmation.existingRole.name}`);
        
        // This would integrate with the existing role assignment system
        return {
            success: true,
            action: 'use_existing_role',
            role: confirmation.existingRole,
            message: `✅ Admin approved using existing role "${confirmation.existingRole.name}"`,
            ephemeral: true
        };
    }

    /**
     * Handle creating new role
     * @param {Object} confirmation - Confirmation data
     * @param {Object} interaction - Discord interaction
     * @returns {Object} Result
     */
    async handleCreateNewRole(confirmation, interaction) {
        console.log(`[ADMIN_CONFIRMATION] Admin chose to create new role: ${confirmation.intendedName}`);
        
        // This would integrate with the existing role creation system
        return {
            success: true,
            action: 'create_new_role',
            roleName: confirmation.intendedName,
            message: `✅ Admin chose to create new role "${confirmation.intendedName}"`,
            ephemeral: true
        };
    }

    /**
     * Handle showing detailed analysis
     * @param {Object} confirmation - Confirmation data
     * @param {Object} interaction - Discord interaction
     * @returns {Object} Result
     */
    async handleShowDetails(confirmation, interaction) {
        const detailsEmbed = {
            color: 0x3498DB, // Blue for information
            title: '📋 Detailed Analysis',
            fields: [
                {
                    name: '🔍 Pattern Analysis',
                    value: confirmation.analysis.detectedPatterns.map(pattern => 
                        `• **${pattern.type}**: \`${pattern.pattern}\` (${pattern.position})`
                    ).join('\n') || 'No specific patterns detected',
                    inline: false
                },
                {
                    name: '📝 Word Comparison',
                    value: `**Intended words**: ${confirmation.analysis.intendedWords.join(', ')}\n**Existing words**: ${confirmation.analysis.existingWords.join(', ')}`,
                    inline: false
                },
                {
                    name: '➕ Extra Elements',
                    value: confirmation.analysis.extraWords.length > 0 ? 
                        confirmation.analysis.extraWords.join(', ') : 
                        'None detected',
                    inline: true
                },
                {
                    name: '➖ Missing Elements',
                    value: confirmation.analysis.missingWords.length > 0 ? 
                        confirmation.analysis.missingWords.join(', ') : 
                        'None detected',
                    inline: true
                }
            ],
            footer: {
                text: 'Return to the previous message to make your decision'
            }
        };
        
        return {
            success: true,
            embed: detailsEmbed,
            ephemeral: true
        };
    }

    /**
     * Handle confirmation timeout
     * @param {string} sessionId - Session ID
     */
    handleConfirmationTimeout(sessionId) {
        const confirmation = this.pendingConfirmations.get(sessionId);
        
        if (confirmation) {
            console.log(`[ADMIN_CONFIRMATION] Confirmation timed out: ${sessionId}`);
            
            this.pendingConfirmations.delete(sessionId);
            this.stats.totalConfirmationsTimedOut++;
            
            // Emit timeout event
            this.emit('confirmationTimeout', {
                sessionId,
                intendedName: confirmation.intendedName,
                existingName: confirmation.existingName
            });
            
            // Handle auto-approval if configured
            if (this.config.autoApproveIfNoAdmin) {
                this.emit('autoApproval', {
                    sessionId,
                    action: 'use_existing_role',
                    reason: 'Admin confirmation timeout with auto-approval enabled'
                });
            }
        }
    }

    /**
     * Clean up old confirmations
     */
    cleanupOldConfirmations() {
        const cutoff = Date.now() - this.config.confirmationTimeout;
        
        for (const [sessionId, confirmation] of this.pendingConfirmations) {
            if (confirmation.timestamp < cutoff) {
                clearTimeout(confirmation.timeout);
                this.pendingConfirmations.delete(sessionId);
                console.log(`[ADMIN_CONFIRMATION] Cleaned up old confirmation: ${sessionId}`);
            }
        }
    }

    /**
     * Update average response time
     * @param {number} responseTime - Response time in milliseconds
     */
    updateAverageResponseTime(responseTime) {
        const totalResponses = this.stats.totalConfirmationsApproved + this.stats.totalConfirmationsRejected;
        this.stats.averageResponseTime = 
            (this.stats.averageResponseTime * (totalResponses - 1) + responseTime) / totalResponses;
    }

    /**
     * Get confirmation statistics
     * @returns {Object} Statistics
     */
    getStatistics() {
        return {
            ...this.stats,
            pendingConfirmations: this.pendingConfirmations.size,
            config: this.config
        };
    }

    /**
     * Update configuration
     * @param {Object} newConfig - New configuration
     */
    updateConfiguration(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('[ADMIN_CONFIRMATION] Configuration updated');
    }
}

module.exports = new AdminConfirmationManager();
