/**
 * Enhanced Admin Interface for Role Matching
 * Provides comprehensive UI for role matching decisions with detailed information
 */

const {
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle,
    MessageFlags
} = require('discord.js');

class EnhancedAdminInterface {
    constructor() {
        this.sessionStore = new Map();
        this.confirmationStore = new Map();
    }
    
    /**
     * Create the main role selection interface
     * @param {string} intendedRoleName - The intended role name
     * @param {string} channelName - The channel name
     * @param {Array} matches - Array of role matches
     * @param {string} sessionId - Unique session ID
     * @param {Object} config - Configuration object
     * @returns {Object} Object containing embed and components
     */
    createRoleSelectionInterface(intendedRoleName, channelName, matches, sessionId, config) {
        const embed = this.createMainEmbed(intendedRoleName, channelName, matches);
        const components = this.createMainComponents(matches, sessionId, config);
        
        return { embed, components };
    }
    
    /**
     * Create the main embed with detailed match information
     */
    createMainEmbed(intendedRoleName, channelName, matches) {
        const embed = new EmbedBuilder()
            .setTitle('🔍 Role Matching Analysis')
            .setColor(0x3498DB) // Blue color for information
            .setDescription(
                `**Channel:** \`${channelName}\`\n` +
                `**Intended Role:** \`${intendedRoleName}\`\n\n` +
                `I found **${matches.length}** similar role${matches.length > 1 ? 's' : ''} that might match what you're trying to create:\n\n` +
                this.formatMatchDetails(matches) +
                `\n**What would you like to do?**`
            )
            .setFooter({ 
                text: `Role Management • Session expires in 10 minutes • ${matches.length}/${matches.length} matches shown`
            })
            .setTimestamp();
        
        return embed;
    }
    
    /**
     * Format detailed match information
     */
    formatMatchDetails(matches) {
        return matches.map((match, index) => {
            const roleInfo = this.getRoleInfo(match.role);
            const matchInfo = this.getMatchInfo(match);
            
            return `**${index + 1}.** ${match.role} ${this.getMatchTypeEmoji(match.matchType)}\n` +
                   `   📊 **Score:** ${Math.round(match.score)}% (${match.details})\n` +
                   `   📅 **Created:** <t:${Math.floor(match.role.createdTimestamp / 1000)}:R>\n` +
                   `   👥 **Members:** ${match.role.members.size}\n` +
                   `   🎨 **Color:** ${roleInfo.colorHex}\n` +
                   `   🔒 **Permissions:** ${roleInfo.permissionSummary}\n` +
                   `   🧮 **Algorithms:** ${matchInfo}`;
        }).join('\n\n');
    }
    
    /**
     * Get role information summary
     */
    getRoleInfo(role) {
        const colorHex = role.color ? `#${role.color.toString(16).padStart(6, '0')}` : 'Default';
        const permissions = role.permissions.toArray();
        const importantPerms = permissions.filter(perm => 
            ['Administrator', 'ManageGuild', 'ManageRoles', 'ManageChannels'].includes(perm)
        );
        const permissionSummary = importantPerms.length > 0 ? 
            importantPerms.slice(0, 2).join(', ') + (importantPerms.length > 2 ? '...' : '') :
            'Basic permissions';
        
        return { colorHex, permissionSummary };
    }
    
    /**
     * Get match algorithm information
     */
    getMatchInfo(match) {
        const algorithms = match.algorithms || {};
        const topAlgorithms = Object.entries(algorithms)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 2)
            .map(([name, score]) => `${name}(${Math.round(score)}%)`)
            .join(', ');
        
        return topAlgorithms || 'N/A';
    }
    
    /**
     * Get emoji for match type
     */
    getMatchTypeEmoji(matchType) {
        const emojis = {
            exact: '🎯',
            levenshtein: '📝',
            word_overlap: '🔗',
            substring: '🔍',
            partial: '🧩',
            fuzzy: '🌟'
        };
        return emojis[matchType] || '❓';
    }
    
    /**
     * Create main action components
     */
    createMainComponents(matches, sessionId, config) {
        const components = [];
        
        // Role selection dropdown (if multiple matches)
        if (matches.length > 1) {
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`enhanced_role_select_${sessionId}`)
                .setPlaceholder('🎯 Choose which role to use...')
                .setMinValues(1)
                .setMaxValues(1);

            // Limit to top 3 matches for cleaner interface
            const topMatches = matches.slice(0, Math.min(3, matches.length));

            topMatches.forEach((match, index) => {
                const truncatedName = match.role.name.length > 80 ?
                    match.role.name.substring(0, 77) + '...' :
                    match.role.name;

                const truncatedDesc = `${Math.round(match.score)}% match • ${match.role.members.size} members • ${match.matchType}`;

                selectMenu.addOptions(
                    new StringSelectMenuOptionBuilder()
                        .setLabel(truncatedName)
                        .setDescription(truncatedDesc.length > 100 ? truncatedDesc.substring(0, 97) + '...' : truncatedDesc)
                        .setValue(`${index}`)
                        .setEmoji(this.getMatchTypeEmoji(match.matchType))
                );
            });

            components.push(new ActionRowBuilder().addComponents(selectMenu));
        }
        
        // Main action buttons
        const mainRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`enhanced_role_use_${sessionId}`)
                    .setLabel(matches.length > 1 ? 'Use Selected Role' : 'Use This Role')
                    .setStyle(ButtonStyle.Success)
                    .setEmoji('✅'),
                new ButtonBuilder()
                    .setCustomId(`enhanced_role_create_${sessionId}`)
                    .setLabel('Create New Role')
                    .setStyle(ButtonStyle.Primary)
                    .setEmoji('➕'),
                new ButtonBuilder()
                    .setCustomId(`enhanced_role_modify_${sessionId}`)
                    .setLabel('Create with New Name')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('✏️')
            );
        
        components.push(mainRow);
        
        // Secondary action buttons
        const secondaryRow = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`enhanced_role_replace_${sessionId}`)
                    .setLabel('Replace Existing')
                    .setStyle(ButtonStyle.Danger)
                    .setEmoji('🔄')
                    .setDisabled(!config.requireConfirmationForReplace),
                new ButtonBuilder()
                    .setCustomId(`enhanced_role_cancel_${sessionId}`)
                    .setLabel('Cancel Operation')
                    .setStyle(ButtonStyle.Secondary)
                    .setEmoji('❌')
            );
        
        components.push(secondaryRow);
        
        return components;
    }
    
    /**
     * Create confirmation dialog for destructive actions
     */
    createConfirmationDialog(action, sessionData, selectedRole = null) {
        const embed = new EmbedBuilder()
            .setTitle('⚠️ Confirmation Required')
            .setColor(0xE74C3C); // Red color for warning
        
        let description = '';
        let confirmLabel = '';
        let confirmStyle = ButtonStyle.Danger;
        
        switch (action) {
            case 'replace':
                const roleToReplace = selectedRole || sessionData.matches[0].role;
                description = `**You are about to replace an existing role:**\n\n` +
                            `🗑️ **Role to delete:** ${roleToReplace}\n` +
                            `👥 **Members affected:** ${roleToReplace.members.size}\n` +
                            `📅 **Role age:** <t:${Math.floor(roleToReplace.createdTimestamp / 1000)}:R>\n\n` +
                            `➕ **New role name:** \`${sessionData.intendedRoleName}\`\n\n` +
                            `**⚠️ This action cannot be undone!**\n` +
                            `Members with the old role will lose it and need to be reassigned.`;
                confirmLabel = 'Replace Role';
                break;
                
            case 'cancel':
                description = `**You are about to cancel the operation:**\n\n` +
                            `📍 **Channel:** \`${sessionData.channelName}\`\n` +
                            `👥 **Intended role:** \`${sessionData.intendedRoleName}\`\n\n` +
                            `No changes will be made to your server.`;
                confirmLabel = 'Cancel Operation';
                confirmStyle = ButtonStyle.Secondary;
                break;
        }
        
        embed.setDescription(description);
        
        const confirmationId = `confirm_${action}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const components = [
            new ActionRowBuilder()
                .addComponents(
                    new ButtonBuilder()
                        .setCustomId(`enhanced_confirm_yes_${confirmationId}`)
                        .setLabel(confirmLabel)
                        .setStyle(confirmStyle)
                        .setEmoji('✅'),
                    new ButtonBuilder()
                        .setCustomId(`enhanced_confirm_no_${confirmationId}`)
                        .setLabel('Go Back')
                        .setStyle(ButtonStyle.Secondary)
                        .setEmoji('↩️')
                )
        ];
        
        return { embed, components, confirmationId };
    }
    
    /**
     * Create modal for custom role name input
     */
    createCustomNameModal(sessionId, currentName) {
        const modal = new ModalBuilder()
            .setCustomId(`enhanced_role_name_modal_${sessionId}`)
            .setTitle('Create Role with Custom Name');
        
        const nameInput = new TextInputBuilder()
            .setCustomId('customRoleName')
            .setLabel('New Role Name')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter the new role name...')
            .setValue(currentName)
            .setRequired(true)
            .setMaxLength(100);
        
        const reasonInput = new TextInputBuilder()
            .setCustomId('customReason')
            .setLabel('Reason for Custom Name (Optional)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Why are you using a different name?')
            .setRequired(false)
            .setMaxLength(500);
        
        modal.addComponents(
            new ActionRowBuilder().addComponents(nameInput),
            new ActionRowBuilder().addComponents(reasonInput)
        );
        
        return modal;
    }
    
    /**
     * Create success/result embed
     */
    createResultEmbed(action, result, sessionData) {
        const embed = new EmbedBuilder()
            .setTimestamp();
        
        switch (action) {
            case 'use':
                embed
                    .setTitle('✅ Channel Created with Existing Role')
                    .setColor(0x27AE60)
                    .setDescription(
                        `**Channel:** ${result.channel}\n` +
                        `**Role:** ${result.role} (existing)\n` +
                        `**Members with access:** ${result.role.members.size}\n` +
                        `**Match score:** ${Math.round(result.matchScore)}%\n\n` +
                        `The channel is now accessible to all members with the existing role.`
                    );
                break;
                
            case 'create':
                embed
                    .setTitle('✅ New Role and Channel Created')
                    .setColor(0x3498DB)
                    .setDescription(
                        `**Channel:** ${result.channel}\n` +
                        `**Role:** ${result.role} (new)\n` +
                        `**Similar roles found:** ${sessionData.matches.length}\n\n` +
                        `Both the new role and existing similar roles coexist in your server.`
                    );
                break;
                
            case 'modify':
                embed
                    .setTitle('✅ Role Created with Custom Name')
                    .setColor(0x9B59B6)
                    .setDescription(
                        `**Channel:** ${result.channel}\n` +
                        `**Role:** ${result.role} (new)\n` +
                        `**Original name:** \`${sessionData.intendedRoleName}\`\n` +
                        `**Custom reason:** ${result.reason || 'Not specified'}\n\n` +
                        `The role was created with your custom name to avoid conflicts.`
                    );
                break;
                
            case 'replace':
                embed
                    .setTitle('✅ Role Replaced Successfully')
                    .setColor(0xE67E22)
                    .setDescription(
                        `**Channel:** ${result.channel}\n` +
                        `**New Role:** ${result.newRole}\n` +
                        `**Replaced Role:** ${result.oldRole.name}\n` +
                        `**Members transferred:** ${result.membersTransferred}\n\n` +
                        `The old role has been replaced and members have been transferred.`
                    );
                break;
        }
        
        return embed;
    }
    
    /**
     * Store session data
     */
    storeSession(sessionId, sessionData) {
        this.sessionStore.set(sessionId, {
            ...sessionData,
            timestamp: Date.now()
        });
    }
    
    /**
     * Get session data
     */
    getSession(sessionId) {
        return this.sessionStore.get(sessionId);
    }
    
    /**
     * Delete session data
     */
    deleteSession(sessionId) {
        this.sessionStore.delete(sessionId);
    }
    
    /**
     * Store confirmation data
     */
    storeConfirmation(confirmationId, data) {
        this.confirmationStore.set(confirmationId, {
            ...data,
            timestamp: Date.now()
        });
    }
    
    /**
     * Get confirmation data
     */
    getConfirmation(confirmationId) {
        return this.confirmationStore.get(confirmationId);
    }
    
    /**
     * Delete confirmation data
     */
    deleteConfirmation(confirmationId) {
        this.confirmationStore.delete(confirmationId);
    }
    
    /**
     * Clean up expired sessions and confirmations
     */
    cleanup(timeoutMinutes = 10) {
        const now = Date.now();
        const maxAge = timeoutMinutes * 60 * 1000;
        
        // Clean up sessions
        for (const [sessionId, data] of this.sessionStore.entries()) {
            if (now - data.timestamp > maxAge) {
                this.sessionStore.delete(sessionId);
                console.log(`[ENHANCED_INTERFACE] Cleaned up expired session: ${sessionId}`);
            }
        }
        
        // Clean up confirmations
        for (const [confirmationId, data] of this.confirmationStore.entries()) {
            if (now - data.timestamp > maxAge) {
                this.confirmationStore.delete(confirmationId);
                console.log(`[ENHANCED_INTERFACE] Cleaned up expired confirmation: ${confirmationId}`);
            }
        }
    }
    
    /**
     * Generate unique session ID
     */
    generateSessionId(userId, guildId) {
        return `enhanced_${userId}_${guildId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

module.exports = new EnhancedAdminInterface();
