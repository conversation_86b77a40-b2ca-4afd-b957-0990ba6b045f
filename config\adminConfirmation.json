{"adminConfirmation": {"enabled": true, "description": "Controls admin confirmation for role matching with extra text elements", "similarityThresholdForConfirmation": 80, "maxPendingConfirmations": 50, "confirmationTimeout": 300000, "highlightDifferences": true, "autoApproveIfNoAdmin": false, "extraTextPatterns": {"suffixes": ["\\s+(idb|temp|backup|old|new|test|dev|prod|beta|alpha)$", "\\s+\\[(verified|premium|vip|special|admin|mod)\\]$", "\\s+\\((active|inactive|pending|trial)\\)$", "-+(temp|backup|old|new|test|dev|prod|beta|alpha)$", "_+(temp|backup|old|new|test|dev|prod|beta|alpha)$"], "prefixes": ["^(temp|backup|old|new|test|dev|prod|beta|alpha)\\s+", "^(ex|former|previous)\\s+", "^\\[(verified|premium|vip|special)\\]\\s+", "^\\((active|inactive|pending|trial)\\)\\s+"], "insertions": ["\\s+(v\\d+|\\d+\\.\\d+)\\s+", "\\s+\\[(.*?)\\]\\s+", "\\s+\\((.*?)\\)\\s+", "\\s+-\\s+(.*?)\\s+-\\s+"]}, "examples": {"requiresConfirmation": [{"intended": "level 5", "existing": "level 5 idb", "reason": "Contains 'idb' suffix"}, {"intended": "admin", "existing": "admin-temp", "reason": "Contains 'temp' suffix"}, {"intended": "mod team", "existing": "mod team [verified]", "reason": "Contains '[verified]' suffix"}, {"intended": "support", "existing": "temp support", "reason": "Contains 'temp' prefix"}, {"intended": "developer", "existing": "developer (active)", "reason": "Contains '(active)' suffix"}], "automaticApproval": [{"intended": "moderator", "existing": "Moderator", "reason": "Only case difference"}, {"intended": "level-1", "existing": "level 1", "reason": "Only formatting difference"}, {"intended": "vip member", "existing": "VIP Member", "reason": "Only case and formatting difference"}]}}, "guildSpecificSettings": {"description": "Guild-specific overrides for admin confirmation settings", "example_guild_id": {"enabled": true, "similarityThresholdForConfirmation": 85, "autoApproveIfNoAdmin": true, "customPatterns": {"suffixes": ["\\s+custom$"], "prefixes": ["^custom\\s+"]}}}, "adminRoles": {"description": "Roles that can respond to admin confirmation requests", "defaultRoles": ["Administrator", "Admin", "Server Admin", "Owner", "Manager", "Moderator", "Mod"], "permissions": ["MANAGE_ROLES", "MANAGE_CHANNELS", "ADMINISTRATOR"]}, "notifications": {"description": "Notification settings for admin confirmations", "enabled": true, "channels": {"logChannel": null, "adminChannel": null}, "mentions": {"mentionAdmins": false, "mentionRoles": []}}, "analytics": {"description": "Analytics and tracking for admin confirmations", "enabled": true, "trackResponseTimes": true, "trackApprovalRates": true, "trackCommonPatterns": true, "retentionDays": 30}}