/**
 * Automatic Role Selector
 * Implements fully automatic role selection with smart priority system
 * No user interaction required - completely seamless operation
 */

const roleMatchingConfig = require('./roleMatchingConfig');
const enhancedRoleMatchingEngine = require('./enhancedRoleMatchingEngine');

class AutomaticRoleSelector {
    constructor() {
        this.selectionHistory = new Map();
    }

    /**
     * Automatically select the best matching role for a channel name
     * @param {string} intendedRoleName - The intended role name
     * @param {Collection} existingRoles - Collection of existing guild roles
     * @param {string} guildId - Guild ID for configuration
     * @returns {Object} Automatic selection result
     */
    async selectBestRole(intendedRoleName, existingRoles, guildId) {
        const config = roleMatchingConfig.getConfig(guildId);
        
        console.log(`[AUTOMATIC_ROLE_SELECTOR] Processing "${intendedRoleName}" with automatic selection`);
        
        // Find all potential matches
        const matches = enhancedRoleMatchingEngine.findMatches(
            intendedRoleName,
            existingRoles,
            guildId
        );
        
        console.log(`[AUTOMATIC_ROLE_SELECTOR] Found ${matches.length} potential matches`);
        
        // Filter matches by minimum threshold
        const validMatches = matches.filter(match => 
            match.score >= config.minimumMatchThreshold
        );
        
        console.log(`[AUTOMATIC_ROLE_SELECTOR] ${validMatches.length} matches above ${config.minimumMatchThreshold}% threshold`);
        
        if (validMatches.length === 0) {
            // No valid matches - automatically create new role
            return {
                action: 'create_new',
                reason: `No existing roles found above ${config.minimumMatchThreshold}% similarity threshold`,
                selectedRole: null,
                score: 0,
                priority: 'none',
                automatic: true
            };
        }
        
        // Apply smart priority system and select best match
        const bestMatch = this.selectByPriority(validMatches, config);
        
        console.log(`[AUTOMATIC_ROLE_SELECTOR] Selected "${bestMatch.role.name}" (${bestMatch.score}% match, ${bestMatch.priority} priority)`);
        
        // Record selection for analytics
        this.recordSelection(guildId, intendedRoleName, bestMatch);
        
        return {
            action: 'use_existing',
            reason: `Automatically selected best match: ${bestMatch.score}% similarity (${bestMatch.priority} priority)`,
            selectedRole: bestMatch.role,
            score: bestMatch.score,
            priority: bestMatch.priority,
            matchType: bestMatch.matchType,
            automatic: true
        };
    }

    /**
     * Select role by smart priority system
     * @param {Array} matches - Array of valid matches
     * @param {Object} config - Configuration object
     * @returns {Object} Best match based on priority
     */
    selectByPriority(matches, config) {
        // Sort matches by priority and score
        const prioritizedMatches = matches.map(match => {
            const priority = this.determinePriority(match.score, config);
            return { ...match, priority };
        });
        
        // Sort by priority level first, then by score within same priority
        prioritizedMatches.sort((a, b) => {
            const priorityOrder = { 'exact': 1, 'high': 2, 'medium': 3, 'low': 4 };
            
            if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
                return priorityOrder[a.priority] - priorityOrder[b.priority];
            }
            
            // Same priority - sort by score (higher first)
            return b.score - a.score;
        });
        
        console.log(`[AUTOMATIC_ROLE_SELECTOR] Priority ranking:`);
        prioritizedMatches.forEach((match, index) => {
            console.log(`  ${index + 1}. "${match.role.name}" - ${match.score}% (${match.priority} priority)`);
        });
        
        return prioritizedMatches[0]; // Return highest priority match
    }

    /**
     * Determine priority level based on score
     * @param {number} score - Match score (0-100)
     * @param {Object} config - Configuration object
     * @returns {string} Priority level
     */
    determinePriority(score, config) {
        if (score >= config.exactMatchThreshold) {
            return 'exact';
        } else if (score >= config.highPriorityThreshold) {
            return 'high';
        } else if (score >= config.mediumPriorityThreshold) {
            return 'medium';
        } else if (score >= config.lowPriorityThreshold) {
            return 'low';
        } else {
            return 'below_threshold';
        }
    }

    /**
     * Record selection for analytics and learning
     * @param {string} guildId - Guild ID
     * @param {string} intendedName - Intended role name
     * @param {Object} selectedMatch - Selected match object
     */
    recordSelection(guildId, intendedName, selectedMatch) {
        const key = `${guildId}_${intendedName.toLowerCase()}`;
        
        if (!this.selectionHistory.has(key)) {
            this.selectionHistory.set(key, []);
        }
        
        this.selectionHistory.get(key).push({
            timestamp: Date.now(),
            intendedName,
            selectedRole: selectedMatch.role.name,
            score: selectedMatch.score,
            priority: selectedMatch.priority,
            matchType: selectedMatch.matchType
        });
        
        // Keep only last 10 selections per role name
        const history = this.selectionHistory.get(key);
        if (history.length > 10) {
            history.splice(0, history.length - 10);
        }
    }

    /**
     * Get selection statistics for a guild
     * @param {string} guildId - Guild ID
     * @returns {Object} Selection statistics
     */
    getSelectionStats(guildId) {
        const guildSelections = Array.from(this.selectionHistory.entries())
            .filter(([key]) => key.startsWith(guildId))
            .flatMap(([, selections]) => selections);
        
        if (guildSelections.length === 0) {
            return {
                totalSelections: 0,
                averageScore: 0,
                priorityDistribution: {},
                mostCommonMatches: []
            };
        }
        
        const totalScore = guildSelections.reduce((sum, sel) => sum + sel.score, 0);
        const averageScore = totalScore / guildSelections.length;
        
        const priorityDistribution = guildSelections.reduce((dist, sel) => {
            dist[sel.priority] = (dist[sel.priority] || 0) + 1;
            return dist;
        }, {});
        
        const roleFrequency = guildSelections.reduce((freq, sel) => {
            freq[sel.selectedRole] = (freq[sel.selectedRole] || 0) + 1;
            return freq;
        }, {});
        
        const mostCommonMatches = Object.entries(roleFrequency)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([role, count]) => ({ role, count }));
        
        return {
            totalSelections: guildSelections.length,
            averageScore: Math.round(averageScore * 100) / 100,
            priorityDistribution,
            mostCommonMatches
        };
    }

    /**
     * Clean up old selection history
     */
    cleanupHistory() {
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
        const cutoff = Date.now() - maxAge;
        
        for (const [key, selections] of this.selectionHistory.entries()) {
            const filtered = selections.filter(sel => sel.timestamp > cutoff);
            
            if (filtered.length === 0) {
                this.selectionHistory.delete(key);
            } else {
                this.selectionHistory.set(key, filtered);
            }
        }
    }

    /**
     * Validate automatic selection configuration
     * @param {string} guildId - Guild ID
     * @returns {Object} Validation result
     */
    validateConfiguration(guildId) {
        const config = roleMatchingConfig.getConfig(guildId);
        const issues = [];
        
        if (!config.fullyAutomatic) {
            issues.push('Fully automatic mode is not enabled');
        }
        
        if (config.requireUserConfirmation) {
            issues.push('User confirmation is still required');
        }
        
        if (config.minimumMatchThreshold < 70) {
            issues.push('Minimum match threshold is too low (recommended: 80%+)');
        }
        
        if (config.minimumMatchThreshold >= config.exactMatchThreshold) {
            issues.push('Minimum threshold should be lower than exact match threshold');
        }
        
        return {
            valid: issues.length === 0,
            issues,
            config: {
                fullyAutomatic: config.fullyAutomatic,
                minimumThreshold: config.minimumMatchThreshold,
                exactThreshold: config.exactMatchThreshold,
                requireConfirmation: config.requireUserConfirmation
            }
        };
    }
}

module.exports = new AutomaticRoleSelector();
