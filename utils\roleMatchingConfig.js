/**
 * Enhanced Role Matching Configuration System
 * Provides server-specific configuration options for role matching behavior
 */

class RoleMatchingConfig {
    constructor() {
        // Global default configuration
        this.defaultConfig = {
            // Similarity thresholds (0-100)
            similarityThreshold: 70,
            exactMatchThreshold: 95,
            partialMatchThreshold: 60,
            substringMatchThreshold: 50,
            
            // Display options
            maxMatchesToDisplay: 5,
            minMatchesToDisplay: 1,
            
            // Algorithm weights (0-1)
            algorithmWeights: {
                exact: 1.0,
                levenshtein: 0.8,
                wordOverlap: 0.7,
                substring: 0.6,
                partial: 0.5
            },
            
            // Enabled algorithms
            enabledAlgorithms: {
                exact: true,
                levenshtein: true,
                wordOverlap: true,
                substring: true,
                partial: true
            },
            
            // Behavior settings
            autoUseExactMatch: false,
            requireConfirmationForReplace: true,
            sessionTimeoutMinutes: 10,
            
            // Role filtering
            excludeSystemRoles: true,
            excludeBotRoles: true,
            excludeIntegrationRoles: true,
            minRoleAgeHours: 0, // Exclude very new roles
            
            // Performance settings
            maxRolesToScan: 1000,
            cacheResultsMinutes: 5
        };
        
        // Server-specific configurations
        this.serverConfigs = new Map();
        
        // Cache for performance
        this.configCache = new Map();
        this.cacheTimestamps = new Map();
    }
    
    /**
     * Get configuration for a specific server
     * @param {string} guildId - Discord guild ID
     * @returns {Object} Configuration object
     */
    getConfig(guildId) {
        // Check cache first
        const cacheKey = `config_${guildId}`;
        const cached = this.configCache.get(cacheKey);
        const cacheTime = this.cacheTimestamps.get(cacheKey);
        
        if (cached && cacheTime && (Date.now() - cacheTime) < (this.defaultConfig.cacheResultsMinutes * 60 * 1000)) {
            return cached;
        }
        
        // Get server-specific config or use defaults
        const serverConfig = this.serverConfigs.get(guildId) || {};
        const config = { ...this.defaultConfig, ...serverConfig };
        
        // Cache the result
        this.configCache.set(cacheKey, config);
        this.cacheTimestamps.set(cacheKey, Date.now());
        
        return config;
    }
    
    /**
     * Update configuration for a specific server
     * @param {string} guildId - Discord guild ID
     * @param {Object} updates - Configuration updates
     */
    updateConfig(guildId, updates) {
        const currentConfig = this.serverConfigs.get(guildId) || {};
        const newConfig = { ...currentConfig, ...updates };
        
        // Validate configuration values
        this.validateConfig(newConfig);
        
        this.serverConfigs.set(guildId, newConfig);
        
        // Clear cache for this server
        const cacheKey = `config_${guildId}`;
        this.configCache.delete(cacheKey);
        this.cacheTimestamps.delete(cacheKey);
        
        console.log(`[ROLE_CONFIG] Updated configuration for guild ${guildId}`);
    }
    
    /**
     * Validate configuration values
     * @param {Object} config - Configuration to validate
     */
    validateConfig(config) {
        // Validate thresholds (0-100)
        const thresholds = ['similarityThreshold', 'exactMatchThreshold', 'partialMatchThreshold', 'substringMatchThreshold'];
        thresholds.forEach(threshold => {
            if (config[threshold] !== undefined) {
                if (typeof config[threshold] !== 'number' || config[threshold] < 0 || config[threshold] > 100) {
                    throw new Error(`${threshold} must be a number between 0 and 100`);
                }
            }
        });
        
        // Validate display options
        if (config.maxMatchesToDisplay !== undefined) {
            if (typeof config.maxMatchesToDisplay !== 'number' || config.maxMatchesToDisplay < 1 || config.maxMatchesToDisplay > 10) {
                throw new Error('maxMatchesToDisplay must be a number between 1 and 10');
            }
        }
        
        // Validate algorithm weights (0-1)
        if (config.algorithmWeights) {
            Object.entries(config.algorithmWeights).forEach(([algorithm, weight]) => {
                if (typeof weight !== 'number' || weight < 0 || weight > 1) {
                    throw new Error(`Algorithm weight for ${algorithm} must be a number between 0 and 1`);
                }
            });
        }
        
        // Validate session timeout
        if (config.sessionTimeoutMinutes !== undefined) {
            if (typeof config.sessionTimeoutMinutes !== 'number' || config.sessionTimeoutMinutes < 1 || config.sessionTimeoutMinutes > 60) {
                throw new Error('sessionTimeoutMinutes must be a number between 1 and 60');
            }
        }
    }
    
    /**
     * Reset configuration for a server to defaults
     * @param {string} guildId - Discord guild ID
     */
    resetConfig(guildId) {
        this.serverConfigs.delete(guildId);
        
        // Clear cache
        const cacheKey = `config_${guildId}`;
        this.configCache.delete(cacheKey);
        this.cacheTimestamps.delete(cacheKey);
        
        console.log(`[ROLE_CONFIG] Reset configuration for guild ${guildId} to defaults`);
    }
    
    /**
     * Get all server configurations (for admin purposes)
     * @returns {Map} Map of guild IDs to configurations
     */
    getAllConfigs() {
        return new Map(this.serverConfigs);
    }
    
    /**
     * Clean up old cache entries
     */
    cleanupCache() {
        const now = Date.now();
        const maxAge = this.defaultConfig.cacheResultsMinutes * 60 * 1000;
        
        for (const [key, timestamp] of this.cacheTimestamps.entries()) {
            if (now - timestamp > maxAge) {
                this.configCache.delete(key);
                this.cacheTimestamps.delete(key);
            }
        }
    }
}

// Create singleton instance
const roleMatchingConfig = new RoleMatchingConfig();

// Set up cache cleanup interval
setInterval(() => {
    roleMatchingConfig.cleanupCache();
}, 5 * 60 * 1000); // Clean up every 5 minutes

    /**
     * Export configuration for backup/restore
     * @param {string} guildId - Discord guild ID
     * @returns {Object} Configuration export
     */
    exportConfig(guildId) {
        const config = this.serverConfigs.get(guildId) || {};
        return {
            guildId,
            config,
            exportedAt: new Date().toISOString(),
            version: '1.0'
        };
    }

    /**
     * Import configuration from backup
     * @param {Object} exportData - Configuration export data
     */
    importConfig(exportData) {
        if (!exportData.guildId || !exportData.config) {
            throw new Error('Invalid export data format');
        }

        this.validateConfig(exportData.config);
        this.updateConfig(exportData.guildId, exportData.config);

        console.log(`[ROLE_CONFIG] Imported configuration for guild ${exportData.guildId}`);
    }

    /**
     * Get configuration template for new servers
     * @returns {Object} Template configuration
     */
    getTemplate() {
        return {
            ...this.defaultConfig,
            _template: true,
            _description: 'Template configuration for enhanced role matching system'
        };
    }

    /**
     * Apply preset configuration
     * @param {string} guildId - Discord guild ID
     * @param {string} preset - Preset name ('strict', 'balanced', 'permissive')
     */
    applyPreset(guildId, preset) {
        const presets = {
            strict: {
                similarityThreshold: 85,
                exactMatchThreshold: 98,
                partialMatchThreshold: 75,
                substringMatchThreshold: 70,
                maxMatchesToDisplay: 3,
                autoUseExactMatch: true,
                algorithmWeights: {
                    exact: 1.0,
                    levenshtein: 0.9,
                    wordOverlap: 0.8,
                    substring: 0.4,
                    partial: 0.3
                }
            },
            balanced: {
                ...this.defaultConfig
            },
            permissive: {
                similarityThreshold: 50,
                exactMatchThreshold: 90,
                partialMatchThreshold: 40,
                substringMatchThreshold: 30,
                maxMatchesToDisplay: 5,
                autoUseExactMatch: false,
                algorithmWeights: {
                    exact: 1.0,
                    levenshtein: 0.7,
                    wordOverlap: 0.8,
                    substring: 0.8,
                    partial: 0.7
                }
            }
        };

        if (!presets[preset]) {
            throw new Error(`Unknown preset: ${preset}. Available: ${Object.keys(presets).join(', ')}`);
        }

        this.updateConfig(guildId, presets[preset]);
        console.log(`[ROLE_CONFIG] Applied ${preset} preset to guild ${guildId}`);
    }
}

module.exports = roleMatchingConfig;
