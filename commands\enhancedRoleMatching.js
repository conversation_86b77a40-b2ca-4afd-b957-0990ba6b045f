/**
 * Enhanced Role Matching Command
 * Provides configuration and testing capabilities for the enhanced role matching system
 */

const { <PERSON><PERSON><PERSON>ommandBuilder, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, PermissionFlagsBits } = require('discord.js');
const roleMatchingConfig = require('../utils/roleMatchingConfig');
const enhancedRoleMatchingValidator = require('../utils/enhancedRoleMatchingValidator');
const enhancedRoleMatchingHandler = require('../utils/enhancedRoleMatchingHandler');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('enhanced-role-matching')
        .setDescription('Configure and test the enhanced role matching system')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles)
        .addSubcommand(subcommand =>
            subcommand
                .setName('config')
                .setDescription('Configure role matching settings')
                .addStringOption(option =>
                    option.setName('preset')
                        .setDescription('Apply a configuration preset')
                        .addChoices(
                            { name: 'Strict (High accuracy, fewer matches)', value: 'strict' },
                            { name: 'Balanced (Default settings)', value: 'balanced' },
                            { name: 'Permissive (More matches, lower accuracy)', value: 'permissive' }
                        ))
                .addIntegerOption(option =>
                    option.setName('similarity-threshold')
                        .setDescription('Minimum similarity percentage for matches (0-100)')
                        .setMinValue(0)
                        .setMaxValue(100))
                .addIntegerOption(option =>
                    option.setName('max-matches')
                        .setDescription('Maximum number of matches to display (1-10)')
                        .setMinValue(1)
                        .setMaxValue(10)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('test')
                .setDescription('Test role matching with a sample name')
                .addStringOption(option =>
                    option.setName('role-name')
                        .setDescription('Role name to test matching against')
                        .setRequired(true)))
        .addSubcommand(subcommand =>
            subcommand
                .setName('validate')
                .setDescription('Run comprehensive system validation tests'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('status')
                .setDescription('Show current configuration and system status'))
        .addSubcommand(subcommand =>
            subcommand
                .setName('reset')
                .setDescription('Reset configuration to defaults')),

    async execute(interaction) {
        const subcommand = interaction.options.getSubcommand();

        try {
            switch (subcommand) {
                case 'config':
                    await this.handleConfig(interaction);
                    break;
                case 'test':
                    await this.handleTest(interaction);
                    break;
                case 'validate':
                    await this.handleValidate(interaction);
                    break;
                case 'status':
                    await this.handleStatus(interaction);
                    break;
                case 'reset':
                    await this.handleReset(interaction);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Unknown subcommand.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            console.error('[ENHANCED_ROLE_MATCHING_CMD] Error:', error);
            
            const errorMessage = error.message.includes('Missing Permissions') 
                ? 'I don\'t have the required permissions to perform this action.'
                : 'An error occurred while processing your request.';
            
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({
                    content: `❌ ${errorMessage}`,
                    ephemeral: true
                });
            } else {
                await interaction.reply({
                    content: `❌ ${errorMessage}`,
                    ephemeral: true
                });
            }
        }
    },

    async handleConfig(interaction) {
        const preset = interaction.options.getString('preset');
        const similarityThreshold = interaction.options.getInteger('similarity-threshold');
        const maxMatches = interaction.options.getInteger('max-matches');

        await interaction.deferReply({ ephemeral: true });

        try {
            const guildId = interaction.guild.id;
            let changes = [];

            // Apply preset if specified
            if (preset) {
                roleMatchingConfig.applyPreset(guildId, preset);
                changes.push(`Applied ${preset} preset`);
            }

            // Apply individual settings
            const updates = {};
            if (similarityThreshold !== null) {
                updates.similarityThreshold = similarityThreshold;
                changes.push(`Similarity threshold: ${similarityThreshold}%`);
            }
            if (maxMatches !== null) {
                updates.maxMatchesToDisplay = maxMatches;
                changes.push(`Max matches: ${maxMatches}`);
            }

            if (Object.keys(updates).length > 0) {
                roleMatchingConfig.updateConfig(guildId, updates);
            }

            const config = roleMatchingConfig.getConfig(guildId);

            const embed = new EmbedBuilder()
                .setTitle('✅ Configuration Updated')
                .setColor(0x00FF00)
                .setDescription(
                    changes.length > 0 
                        ? `**Changes applied:**\n${changes.map(c => `• ${c}`).join('\n')}`
                        : 'No changes were made.'
                )
                .addFields(
                    { name: 'Current Settings', value: 
                        `**Similarity Threshold:** ${config.similarityThreshold}%\n` +
                        `**Max Matches:** ${config.maxMatchesToDisplay}\n` +
                        `**Auto-use Exact Match:** ${config.autoUseExactMatch ? 'Yes' : 'No'}`
                    }
                )
                .setTimestamp();

            await interaction.followUp({ embeds: [embed], ephemeral: true });

        } catch (error) {
            await interaction.followUp({
                content: `❌ Configuration error: ${error.message}`,
                ephemeral: true
            });
        }
    },

    async handleTest(interaction) {
        const roleName = interaction.options.getString('role-name');
        
        await interaction.deferReply({ ephemeral: true });

        try {
            const startTime = Date.now();
            
            // Test the role matching
            const context = {
                guild: interaction.guild,
                guildId: interaction.guild.id,
                userId: interaction.user.id,
                channelName: roleName,
                operationType: 'test'
            };

            const result = await enhancedRoleMatchingHandler.processRoleMatching(
                roleName,
                interaction.guild.roles.cache,
                context
            );

            const processingTime = Date.now() - startTime;

            const embed = new EmbedBuilder()
                .setTitle('🧪 Role Matching Test Results')
                .setColor(0x3498DB)
                .addFields(
                    { name: 'Test Input', value: `\`${roleName}\``, inline: true },
                    { name: 'Processing Time', value: `${processingTime}ms`, inline: true },
                    { name: 'Matches Found', value: result.matches ? result.matches.length.toString() : '0', inline: true }
                );

            if (result.matches && result.matches.length > 0) {
                const matchDetails = result.matches.slice(0, 3).map((match, index) => 
                    `**${index + 1}.** ${match.role.name} (${Math.round(match.score)}% - ${match.matchType})`
                ).join('\n');
                
                embed.addFields({ name: 'Top Matches', value: matchDetails });
            } else {
                embed.addFields({ name: 'Result', value: 'No matches found - would create new role' });
            }

            embed.setTimestamp();

            await interaction.followUp({ embeds: [embed], ephemeral: true });

        } catch (error) {
            await interaction.followUp({
                content: `❌ Test failed: ${error.message}`,
                ephemeral: true
            });
        }
    },

    async handleValidate(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            await interaction.followUp({
                content: '🔍 Running comprehensive validation tests... This may take a moment.',
                ephemeral: true
            });

            const validationResults = await enhancedRoleMatchingValidator.runValidationTests(interaction.guild);
            const report = enhancedRoleMatchingValidator.generateReport(validationResults);

            // Create summary embed
            const embed = new EmbedBuilder()
                .setTitle('🔍 System Validation Results')
                .setColor(validationResults.summary.failed > 0 ? 0xFF0000 : 0x00FF00)
                .addFields(
                    { name: 'Tests Passed', value: `${validationResults.summary.passed}/${validationResults.summary.total}`, inline: true },
                    { name: 'Warnings', value: validationResults.summary.warnings.toString(), inline: true },
                    { name: 'Status', value: validationResults.summary.failed === 0 ? '✅ All tests passed' : '❌ Some tests failed', inline: true }
                );

            if (validationResults.recommendations.length > 0) {
                const highPriorityRecs = validationResults.recommendations.filter(r => r.priority === 'high');
                if (highPriorityRecs.length > 0) {
                    embed.addFields({
                        name: '⚠️ High Priority Recommendations',
                        value: highPriorityRecs.map(r => `• ${r.message}`).join('\n').substring(0, 1024)
                    });
                }
            }

            embed.setTimestamp();

            // Send summary
            await interaction.followUp({ embeds: [embed], ephemeral: true });

            // Send detailed report if there are issues
            if (validationResults.summary.failed > 0 || validationResults.summary.warnings > 0) {
                const reportChunks = this.chunkString(report, 1900);
                for (const chunk of reportChunks) {
                    await interaction.followUp({
                        content: `\`\`\`markdown\n${chunk}\n\`\`\``,
                        ephemeral: true
                    });
                }
            }

        } catch (error) {
            await interaction.followUp({
                content: `❌ Validation failed: ${error.message}`,
                ephemeral: true
            });
        }
    },

    async handleStatus(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            const config = roleMatchingConfig.getConfig(interaction.guild.id);
            const healthCheck = await enhancedRoleMatchingHandler.validateSystemHealth(interaction.guild);
            const performanceStats = enhancedRoleMatchingHandler.getPerformanceStats(interaction.guild.id);

            const embed = new EmbedBuilder()
                .setTitle('📊 Enhanced Role Matching Status')
                .setColor(healthCheck.healthy ? 0x00FF00 : 0xFF0000)
                .addFields(
                    { name: 'System Health', value: healthCheck.healthy ? '✅ Healthy' : '❌ Issues detected', inline: true },
                    { name: 'Manageable Roles', value: `${healthCheck.manageableRoles}/${healthCheck.totalRoles}`, inline: true },
                    { name: 'Similarity Threshold', value: `${config.similarityThreshold}%`, inline: true },
                    { name: 'Max Matches', value: config.maxMatchesToDisplay.toString(), inline: true },
                    { name: 'Auto-use Exact', value: config.autoUseExactMatch ? 'Yes' : 'No', inline: true }
                );

            if (performanceStats) {
                embed.addFields({
                    name: 'Performance (24h)',
                    value: `**Avg Time:** ${performanceStats.averageProcessingTime}ms\n` +
                           `**Operations:** ${performanceStats.totalOperations}\n` +
                           `**Avg Matches:** ${performanceStats.averageMatches}`
                });
            }

            if (!healthCheck.healthy) {
                embed.addFields({
                    name: '⚠️ Issues',
                    value: healthCheck.issues.join('\n').substring(0, 1024)
                });
            }

            embed.setTimestamp();

            await interaction.followUp({ embeds: [embed], ephemeral: true });

        } catch (error) {
            await interaction.followUp({
                content: `❌ Status check failed: ${error.message}`,
                ephemeral: true
            });
        }
    },

    async handleReset(interaction) {
        await interaction.deferReply({ ephemeral: true });

        try {
            roleMatchingConfig.resetConfig(interaction.guild.id);

            const embed = new EmbedBuilder()
                .setTitle('🔄 Configuration Reset')
                .setColor(0x00FF00)
                .setDescription('All settings have been reset to default values.')
                .addFields({
                    name: 'Default Settings',
                    value: '**Similarity Threshold:** 70%\n' +
                           '**Max Matches:** 5\n' +
                           '**Auto-use Exact Match:** No'
                })
                .setTimestamp();

            await interaction.followUp({ embeds: [embed], ephemeral: true });

        } catch (error) {
            await interaction.followUp({
                content: `❌ Reset failed: ${error.message}`,
                ephemeral: true
            });
        }
    },

    chunkString(str, length) {
        const chunks = [];
        for (let i = 0; i < str.length; i += length) {
            chunks.push(str.slice(i, i + length));
        }
        return chunks;
    }
};
