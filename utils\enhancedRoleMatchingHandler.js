/**
 * Enhanced Role Matching Handler
 * Main orchestrator for the enhanced role matching system
 */

const enhancedRoleMatchingEngine = require('./enhancedRoleMatchingEngine');
const enhancedAdminInterface = require('./enhancedAdminInterface');
const smartRoleAssignmentSystem = require('./smartRoleAssignmentSystem');
const roleMatchingConfig = require('./roleMatchingConfig');

class EnhancedRoleMatchingHandler {
    constructor() {
        this.performanceMetrics = new Map();
        this.setupCleanupInterval();
    }
    
    /**
     * Main entry point for role matching process
     * @param {string} intendedRoleName - The intended role name
     * @param {Collection} existingRoles - Collection of existing guild roles
     * @param {Object} context - Context information (guild, user, channel info, etc.)
     * @returns {Object} Result object with matches and interface components
     */
    async processRoleMatching(intendedRoleName, existingRoles, context) {
        const startTime = Date.now();
        
        try {
            // Get configuration for this guild
            const config = roleMatchingConfig.getConfig(context.guildId);
            
            // Find role matches using the enhanced engine
            const matches = enhancedRoleMatchingEngine.findMatches(
                intendedRoleName,
                existingRoles,
                context.guildId
            );
            
            // Record performance metrics
            this.recordPerformance(context.guildId, Date.now() - startTime, matches.length);
            
            // If no matches found and below threshold, proceed with creation
            if (matches.length === 0) {
                return await this.handleNoMatches(intendedRoleName, context, config);
            }
            
            // If exact match found and auto-use is enabled
            if (config.autoUseExactMatch && matches[0].matchType === 'exact') {
                return await this.handleAutoUseExactMatch(matches[0], context);
            }
            
            // Create interactive interface for admin decision
            return await this.createInteractiveInterface(intendedRoleName, matches, context, config);
            
        } catch (error) {
            console.error('[ENHANCED_ROLE_MATCHING] Error in processRoleMatching:', error);
            return {
                success: false,
                error: error.message,
                requiresInteraction: false
            };
        }
    }
    
    /**
     * Handle case when no matches are found
     */
    async handleNoMatches(intendedRoleName, context, config) {
        console.log(`[ENHANCED_ROLE_MATCHING] No matches found for "${intendedRoleName}", proceeding with creation`);
        
        // Create role and channel directly
        const roleResult = await smartRoleAssignmentSystem.createOrUpdateRole({
            guild: context.guild,
            roleName: intendedRoleName,
            roleOptions: context.roleOptions || {},
            action: 'create',
            userId: context.userId,
            sessionId: context.sessionId
        });
        
        if (!roleResult.success) {
            return {
                success: false,
                error: roleResult.error,
                requiresInteraction: false
            };
        }
        
        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: context.guild,
            channelName: context.channelName || intendedRoleName,
            role: roleResult.role,
            channelType: context.channelType || 'text',
            categoryChannel: context.categoryChannel,
            userId: context.userId,
            sessionId: context.sessionId
        });
        
        return {
            success: channelResult.success,
            error: channelResult.error,
            requiresInteraction: false,
            result: {
                action: 'created_new',
                role: roleResult.role,
                channel: channelResult.channel,
                auditIds: [roleResult.auditId, channelResult.auditId]
            }
        };
    }
    
    /**
     * Handle automatic use of exact match
     */
    async handleAutoUseExactMatch(match, context) {
        console.log(`[ENHANCED_ROLE_MATCHING] Auto-using exact match: ${match.role.name}`);
        
        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: context.guild,
            channelName: context.channelName || match.role.name,
            role: match.role,
            channelType: context.channelType || 'text',
            categoryChannel: context.categoryChannel,
            userId: context.userId,
            sessionId: context.sessionId
        });
        
        return {
            success: channelResult.success,
            error: channelResult.error,
            requiresInteraction: false,
            result: {
                action: 'used_existing',
                role: match.role,
                channel: channelResult.channel,
                matchScore: match.score,
                auditIds: [channelResult.auditId]
            }
        };
    }
    
    /**
     * Create interactive interface for admin decision
     */
    async createInteractiveInterface(intendedRoleName, matches, context, config) {
        const sessionId = enhancedAdminInterface.generateSessionId(context.userId, context.guildId);
        
        // Store session data
        enhancedAdminInterface.storeSession(sessionId, {
            userId: context.userId,
            guildId: context.guildId,
            intendedRoleName,
            channelName: context.channelName || intendedRoleName,
            channelType: context.channelType || 'text',
            categoryChannel: context.categoryChannel,
            roleOptions: context.roleOptions || {},
            matches,
            operationType: context.operationType || 'private_channel_creation',
            config
        });
        
        // Create interface components
        const { embed, components } = enhancedAdminInterface.createRoleSelectionInterface(
            intendedRoleName,
            context.channelName || intendedRoleName,
            matches,
            sessionId,
            config
        );
        
        return {
            success: true,
            requiresInteraction: true,
            sessionId,
            embed,
            components,
            matches
        };
    }
    
    /**
     * Handle admin decision from interactive interface
     * @param {Object} interaction - Discord interaction object
     * @returns {Object} Result object
     */
    async handleAdminDecision(interaction) {
        const customId = interaction.customId;
        const parts = customId.split('_');
        
        // Parse interaction type and session ID
        if (!customId.startsWith('enhanced_')) {
            throw new Error('Invalid interaction ID format');
        }
        
        const actionType = parts[2]; // role, confirm
        const action = parts[3]; // use, create, modify, replace, cancel, yes, no
        const sessionId = parts.slice(4).join('_');
        
        console.log(`[ENHANCED_ROLE_MATCHING] Handling decision: ${actionType}_${action} for session: ${sessionId}`);
        
        // Get session data
        const sessionData = enhancedAdminInterface.getSession(sessionId);
        if (!sessionData) {
            return {
                success: false,
                error: 'Session expired or not found. Please try the operation again.',
                ephemeral: true
            };
        }
        
        try {
            let result;
            
            switch (`${actionType}_${action}`) {
                case 'role_use':
                    result = await this.handleUseExistingRole(interaction, sessionData);
                    break;
                case 'role_create':
                    result = await this.handleCreateNewRole(interaction, sessionData);
                    break;
                case 'role_modify':
                    result = await this.handleCreateWithModifiedName(interaction, sessionData);
                    break;
                case 'role_replace':
                    result = await this.handleReplaceExistingRole(interaction, sessionData);
                    break;
                case 'role_cancel':
                    result = await this.handleCancelOperation(interaction, sessionData);
                    break;
                case 'confirm_yes':
                case 'confirm_no':
                    result = await this.handleConfirmationResponse(interaction, sessionData, action === 'yes');
                    break;
                default:
                    throw new Error(`Unknown action: ${actionType}_${action}`);
            }
            
            // Clean up session if operation is complete
            if (result.complete) {
                enhancedAdminInterface.deleteSession(sessionId);
            }
            
            return result;
            
        } catch (error) {
            console.error('[ENHANCED_ROLE_MATCHING] Error handling admin decision:', error);
            enhancedAdminInterface.deleteSession(sessionId);
            
            return {
                success: false,
                error: error.message,
                ephemeral: true,
                complete: true
            };
        }
    }
    
    /**
     * Handle using existing role
     */
    async handleUseExistingRole(interaction, sessionData) {
        // Determine which role to use (from selection or first match)
        let selectedRole = sessionData.matches[0].role;
        let matchScore = sessionData.matches[0].score;
        
        // Check if user selected a specific role
        if (interaction.isStringSelectMenu && interaction.isStringSelectMenu()) {
            const selectedIndex = parseInt(interaction.values[0]);
            if (!isNaN(selectedIndex) && sessionData.matches[selectedIndex]) {
                selectedRole = sessionData.matches[selectedIndex].role;
                matchScore = sessionData.matches[selectedIndex].score;
            }
        }
        
        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: interaction.guild,
            channelName: sessionData.channelName,
            role: selectedRole,
            channelType: sessionData.channelType,
            categoryChannel: sessionData.categoryChannel,
            userId: sessionData.userId,
            sessionId: interaction.customId
        });
        
        if (!channelResult.success) {
            return {
                success: false,
                error: channelResult.error,
                ephemeral: true,
                complete: true
            };
        }
        
        const resultEmbed = enhancedAdminInterface.createResultEmbed('use', {
            channel: channelResult.channel,
            role: selectedRole,
            matchScore
        }, sessionData);
        
        return {
            success: true,
            embed: resultEmbed,
            ephemeral: true,
            complete: true
        };
    }
    
    /**
     * Handle creating new role
     */
    async handleCreateNewRole(interaction, sessionData) {
        const roleResult = await smartRoleAssignmentSystem.createOrUpdateRole({
            guild: interaction.guild,
            roleName: sessionData.intendedRoleName,
            roleOptions: sessionData.roleOptions,
            action: 'create',
            userId: sessionData.userId,
            sessionId: interaction.customId
        });
        
        if (!roleResult.success) {
            return {
                success: false,
                error: roleResult.error,
                ephemeral: true,
                complete: true
            };
        }
        
        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: interaction.guild,
            channelName: sessionData.channelName,
            role: roleResult.role,
            channelType: sessionData.channelType,
            categoryChannel: sessionData.categoryChannel,
            userId: sessionData.userId,
            sessionId: interaction.customId
        });
        
        if (!channelResult.success) {
            // Clean up the role if channel creation failed
            await roleResult.role.delete().catch(console.error);
            return {
                success: false,
                error: channelResult.error,
                ephemeral: true,
                complete: true
            };
        }
        
        const resultEmbed = enhancedAdminInterface.createResultEmbed('create', {
            channel: channelResult.channel,
            role: roleResult.role
        }, sessionData);
        
        return {
            success: true,
            embed: resultEmbed,
            ephemeral: true,
            complete: true
        };
    }
    
    /**
     * Handle creating role with modified name (show modal)
     */
    async handleCreateWithModifiedName(interaction, sessionData) {
        const modal = enhancedAdminInterface.createCustomNameModal(
            interaction.customId.split('_').slice(4).join('_'),
            sessionData.intendedRoleName
        );
        
        return {
            success: true,
            modal,
            ephemeral: true,
            complete: false
        };
    }
    
    /**
     * Handle replacing existing role (show confirmation)
     */
    async handleReplaceExistingRole(interaction, sessionData) {
        // Determine which role to replace
        let roleToReplace = sessionData.matches[0].role;
        
        if (interaction.isStringSelectMenu && interaction.isStringSelectMenu()) {
            const selectedIndex = parseInt(interaction.values[0]);
            if (!isNaN(selectedIndex) && sessionData.matches[selectedIndex]) {
                roleToReplace = sessionData.matches[selectedIndex].role;
            }
        }
        
        const { embed, components, confirmationId } = enhancedAdminInterface.createConfirmationDialog(
            'replace',
            sessionData,
            roleToReplace
        );
        
        // Store confirmation data
        enhancedAdminInterface.storeConfirmation(confirmationId, {
            sessionId: interaction.customId.split('_').slice(4).join('_'),
            action: 'replace',
            roleToReplace,
            sessionData
        });
        
        return {
            success: true,
            embed,
            components,
            ephemeral: true,
            complete: false
        };
    }
    
    /**
     * Handle cancel operation
     */
    async handleCancelOperation(interaction, sessionData) {
        const resultContent = `❌ **Operation cancelled.**\n\nNo changes were made for channel: \`${sessionData.channelName}\``;
        
        return {
            success: true,
            content: resultContent,
            ephemeral: true,
            complete: true
        };
    }
    
    /**
     * Handle confirmation response
     */
    async handleConfirmationResponse(interaction, sessionData, confirmed) {
        if (!confirmed) {
            return {
                success: true,
                content: '↩️ **Returned to main menu.**\n\nPlease make your selection again.',
                ephemeral: true,
                complete: false
            };
        }
        
        // Handle confirmed action based on stored confirmation data
        const confirmationId = interaction.customId.split('_').slice(3).join('_');
        const confirmationData = enhancedAdminInterface.getConfirmation(confirmationId);
        
        if (!confirmationData) {
            return {
                success: false,
                error: 'Confirmation data not found.',
                ephemeral: true,
                complete: true
            };
        }
        
        // Clean up confirmation data
        enhancedAdminInterface.deleteConfirmation(confirmationId);
        
        if (confirmationData.action === 'replace') {
            return await this.executeRoleReplacement(interaction, confirmationData);
        }
        
        return {
            success: false,
            error: 'Unknown confirmation action.',
            ephemeral: true,
            complete: true
        };
    }
    
    /**
     * Execute role replacement
     */
    async executeRoleReplacement(interaction, confirmationData) {
        const { roleToReplace, sessionData } = confirmationData;
        
        const roleResult = await smartRoleAssignmentSystem.createOrUpdateRole({
            guild: interaction.guild,
            roleName: sessionData.intendedRoleName,
            roleOptions: sessionData.roleOptions,
            existingRole: roleToReplace,
            action: 'replace',
            userId: sessionData.userId,
            sessionId: interaction.customId
        });
        
        if (!roleResult.success) {
            return {
                success: false,
                error: roleResult.error,
                ephemeral: true,
                complete: true
            };
        }
        
        const channelResult = await smartRoleAssignmentSystem.createPrivateChannelWithRole({
            guild: interaction.guild,
            channelName: sessionData.channelName,
            role: roleResult.role,
            channelType: sessionData.channelType,
            categoryChannel: sessionData.categoryChannel,
            userId: sessionData.userId,
            sessionId: interaction.customId
        });
        
        if (!channelResult.success) {
            return {
                success: false,
                error: channelResult.error,
                ephemeral: true,
                complete: true
            };
        }
        
        const resultEmbed = enhancedAdminInterface.createResultEmbed('replace', {
            channel: channelResult.channel,
            newRole: roleResult.role,
            oldRole: roleToReplace,
            membersTransferred: roleToReplace.members.size
        }, sessionData);
        
        return {
            success: true,
            embed: resultEmbed,
            ephemeral: true,
            complete: true
        };
    }
    
    /**
     * Record performance metrics
     */
    recordPerformance(guildId, processingTime, matchCount) {
        const key = `perf_${guildId}`;
        const existing = this.performanceMetrics.get(key) || { times: [], matches: [], count: 0 };
        
        existing.times.push(processingTime);
        existing.matches.push(matchCount);
        existing.count++;
        
        // Keep only last 100 measurements
        if (existing.times.length > 100) {
            existing.times.shift();
            existing.matches.shift();
        }
        
        this.performanceMetrics.set(key, existing);
    }
    
    /**
     * Get performance statistics
     */
    getPerformanceStats(guildId) {
        const key = `perf_${guildId}`;
        const data = this.performanceMetrics.get(key);
        
        if (!data || data.times.length === 0) {
            return null;
        }
        
        const avgTime = data.times.reduce((a, b) => a + b, 0) / data.times.length;
        const avgMatches = data.matches.reduce((a, b) => a + b, 0) / data.matches.length;
        const maxTime = Math.max(...data.times);
        const minTime = Math.min(...data.times);
        
        return {
            averageProcessingTime: Math.round(avgTime),
            averageMatches: Math.round(avgMatches * 10) / 10,
            maxProcessingTime: maxTime,
            minProcessingTime: minTime,
            totalOperations: data.count,
            recentOperations: data.times.length
        };
    }
    
    /**
     * Setup cleanup interval for sessions and performance data
     */
    setupCleanupInterval() {
        setInterval(() => {
            try {
                // Clean up admin interface sessions
                enhancedAdminInterface.cleanup();

                // Clean up assignment system audit logs
                smartRoleAssignmentSystem.cleanupAuditLog();

                // Clean up performance metrics (keep only recent data)
                for (const [key, data] of this.performanceMetrics.entries()) {
                    if (data.times.length > 100) {
                        data.times = data.times.slice(-50);
                        data.matches = data.matches.slice(-50);
                    }
                }

                console.log('[ENHANCED_ROLE_MATCHING] Cleanup completed');
            } catch (error) {
                console.error('[ENHANCED_ROLE_MATCHING] Error during cleanup:', error);
            }
        }, 5 * 60 * 1000); // Every 5 minutes
    }

    /**
     * Handle errors gracefully with user-friendly messages
     */
    handleError(error, context = {}) {
        const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;

        console.error(`[ENHANCED_ROLE_MATCHING] Error ${errorId}:`, error);
        console.error('[ENHANCED_ROLE_MATCHING] Context:', context);

        // Log error for debugging
        smartRoleAssignmentSystem.logAssignment({
            type: 'error',
            errorId,
            error: error.message,
            stack: error.stack,
            context,
            timestamp: Date.now()
        });

        // Return user-friendly error message
        let userMessage = 'An unexpected error occurred. ';

        if (error.message.includes('Missing Permissions')) {
            userMessage = 'I don\'t have the required permissions to perform this action. Please check my role permissions.';
        } else if (error.message.includes('Unknown Role') || error.message.includes('Unknown Channel')) {
            userMessage = 'The role or channel no longer exists. It may have been deleted.';
        } else if (error.message.includes('Rate limit')) {
            userMessage = 'Discord rate limit reached. Please try again in a few moments.';
        } else if (error.message.includes('timeout') || error.message.includes('expired')) {
            userMessage = 'The operation timed out. Please try again.';
        } else {
            userMessage += `Error ID: ${errorId}`;
        }

        return {
            success: false,
            error: userMessage,
            errorId,
            ephemeral: true,
            complete: true
        };
    }

    /**
     * Validate system health and requirements
     */
    async validateSystemHealth(guild) {
        const issues = [];

        try {
            // Check bot permissions
            const botMember = guild.members.me;
            if (!botMember) {
                issues.push('Bot member not found in guild');
                return { healthy: false, issues };
            }

            const requiredPermissions = [
                'ManageRoles',
                'ManageChannels',
                'ViewChannel',
                'SendMessages'
            ];

            const missingPermissions = requiredPermissions.filter(perm =>
                !botMember.permissions.has(perm)
            );

            if (missingPermissions.length > 0) {
                issues.push(`Missing permissions: ${missingPermissions.join(', ')}`);
            }

            // Check role hierarchy
            const botHighestRole = botMember.roles.highest;
            const manageableRoles = guild.roles.cache.filter(role =>
                role.comparePositionTo(botHighestRole) < 0 && !role.managed
            );

            if (manageableRoles.size === 0) {
                issues.push('Bot cannot manage any roles due to hierarchy restrictions');
            }

            // Check system components
            try {
                roleMatchingConfig.getConfig(guild.id);
            } catch (error) {
                issues.push(`Configuration system error: ${error.message}`);
            }

            return {
                healthy: issues.length === 0,
                issues,
                manageableRoles: manageableRoles.size,
                totalRoles: guild.roles.cache.size
            };

        } catch (error) {
            issues.push(`Health check failed: ${error.message}`);
            return { healthy: false, issues };
        }
    }
}

module.exports = new EnhancedRoleMatchingHandler();
